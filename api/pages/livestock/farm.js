import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../../base'

const API_PATHS = {
  LIST: 'pasture/manager/pageV2', // 养殖场分页列表
  ADD: 'pasture/addV2', // 新增养殖场
  EDIT: 'pasture/editV2', // 修改养殖场
  DETAIL: 'pasture/manager/info', // 获取养殖场详情
  PENLIST: 'pasture/pen/list', // 获取圈舍列表
  FENCEPAGE: 'pasture/pen/fence/page',// 获取围栏列表
  EARLIST: 'pastureLivestock/list',//耳标活畜
  FENCELIST: 'pasture/pen/fence/page', // 栏位列表
  LIVESTOCKPAGE: 'pastureLivestock/page', // 获取养殖场下活畜列表
  LIVESTOCKVARIETIESLIST: 'livestock/livestockVarieties/list', // 活畜品种列表
  PENPAGE: 'pasture/pen/page',
  PENADD: 'pasture/pen/add',
  PENEDIT: 'pasture/pen/edit',
  PENDETAIL: 'pasture/pen/info',
  PENDELETE: 'pasture/pen/fence/delete',
}
export function pasturePage(param) {
  return request.ajax(xmbtest + API_PATHS.LIST, param, 'POST').then((res) => res.data)
}
export function pastureAdd(param) {
  return request.ajax(xmbtest + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}

export function pastureEdit(param) {
  return request.ajax(xmbtest + API_PATHS.EDIT, param, 'POST').then((res) => res.data)
}

export function pastureDetail(param) {
  return request.ajax(xmbtest + API_PATHS.DETAIL, param, 'POST').then((res) => res.data)
}

export function penList(param) {
  return request.ajax(xmbtest + API_PATHS.PENLIST, param, 'POST').then((res) => res.data)
}
export function fencePage(param) {
  return request.ajax(xmbtest + API_PATHS.FENCEPAGE, param, 'POST').then((res) => res.data)
}
export function earPage(param) {
  return request.ajax(xmbtest + API_PATHS.EARLIST, param, 'POST').then((res) => res.data)
}

export function fenceList(param) {
  return request.ajax(xmbtest + API_PATHS.FENCELIST, param, 'POST').then((res) => res.data)
}

export function livestockPage(param) {
  return request.ajax(xmbtest + API_PATHS.LIVESTOCKPAGE, param, 'POST').then((res) => res.data)
}

export function livestockVarietiesList(param) {
  return request.ajax(xmbtest + API_PATHS.LIVESTOCKVARIETIESLIST, param, 'POST').then((res) => res.data)
}

export function penPage(param) {
  return request.ajax(xmbtest + API_PATHS.PENPAGE, param, 'POST').then((res) => res.data)
}
export function penAdd(param) {
  return request.ajax(xmbtest + API_PATHS.PENADD, param, 'POST').then((res) => res.data)
}
export function penEdit(param) {
  return request.ajax(xmbtest + API_PATHS.PENEDIT, param, 'POST').then((res) => res.data)
}
export function penDetail(param) {
  return request.ajax(xmbtest + API_PATHS.PENDETAIL, param, 'POST').then((res) => res.data)
}
export function penDelete(param) {
  return request.ajax(xmbtest + API_PATHS.PENDELETE, param, 'POST').then((res) => res.data)
}


