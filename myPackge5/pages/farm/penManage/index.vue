<template>
  <view class="container">
    <CustomNavbar title="圈舍管理" :titleColor="'#333333'" />
    <scroll-view
      class="content"
      :style="{ paddingTop: navbarTotalHeight + 'px' }"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <view v-for="item in farmList" :key="item.pastureId" class="farm-item">
        <view class="farm-info" @click="viewDetail(item)">
          <view class="farm-header">
            <text class="farm-name">{{ item.penName }}</text>
          </view>
          <view class="farm-title">
            <text class="farm-label">圈舍编号</text>
            <text class="farm-value">{{ item.penCode }}</text>
          </view>
          <view class="farm-title">
            <text class="farm-label">栏位</text>
            <text class="farm-value">{{ item.fenceNum }}个</text>
          </view>
          <view class="farm-title">
            <text class="farm-label">存栏数量</text>
            <text class="farm-value">{{ item.livestockNum }}头</text>
          </view>
          <!-- <text class="farm-address">
            {{ item.address ||
            `${item.provinceName}${item.cityName}${item.countyName}` }}
          </text>-->
        </view>
      </view>

      <view v-if="loading" class="status-text">加载中...</view>
      <view v-else-if="!hasMore && farmList.length" class="status-text">没有更多数据了</view>
      <view v-else-if="!farmList.length" class="status-text">暂无数据</view>
    </scroll-view>
    <view class="bg-box">
      <view class="add-btn" @click="addFarm">新增圈舍</view>
    </view>
  </view>
</template>

<script>
// import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import CustomNavbar from '../../components/CustomNavbar.vue'
import { penPage } from '@/api/pages/livestock/farm'
import { getDicts } from '@/api/dict.js'
const app = getApp()
export default {
  components: {
    CustomNavbar,
  },
  data() {
    return {
      obs: app.globalData.obs,
      systemInfo: uni.getSystemInfoSync(),
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      farmList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 20,
      pastureNatureList: [],
      pastureId: '',
      pastureName: '',
    }
  },
  onLoad(options) {
    this.pastureId = options.pastureId
    this.pastureName = options.pastureName
    console.log('pastureName', this.pastureName)
    this.loadNatureDict()
    this.loadFarmList()
    // 监听列表更新事件
    uni.$on('updateFarmList', () => {
      this.loadFarmList(true)
    })
  },
  onUnload() {
    // 移除事件监听
    uni.$off('updateFarmList')
  },
  onShow() {},
  computed: {
    navbarTotalHeight() {
      const statusBarHeight = this.systemInfo.statusBarHeight || 0
      const navbarHeight = 64
      return statusBarHeight + navbarHeight
    },
    getpastureNatureName() {
      return (value) => {
        const nature = this.pastureNatureList.find(
          (item) => item.value === value
        )
        return nature ? nature.label : ''
      }
    },
  },
  methods: {
    viewDetail(item) {
      uni.navigateTo({
        url: `/myPackge5/pages/farm/penManage/addForm?penId=${item.penId}&pastureId=${this.pastureId}&pastureName=${this.pastureName}&mode=edit`,
      })
    },
    async loadNatureDict() {
      try {
        const res = await getDicts('pasture_nature')
        if (res && res.data) {
          this.pastureNatureList = res.data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }))
        }
      } catch (error) {
        console.error('加载字典失败:', error)
      }
    },
    async loadFarmList(refresh = false) {
      if (this.loading) return

      this.loading = true
      if (refresh) {
        this.refreshing = true
        this.pageNum = 1
        this.hasMore = true
      }

      try {
        const { result } = await penPage({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          pastureId: this.pastureId,
        })

        const list = result?.list || []
        this.farmList = refresh ? list : [...this.farmList, ...list]
        this.hasMore = !result?.lastPage

        if (this.hasMore) this.pageNum++
      } catch (error) {
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    onRefresh() {
      this.loadFarmList(true)
    },

    onLoadMore() {
      if (this.hasMore && !this.loading) {
        this.loadFarmList()
      }
    },
    addFarm() {
      uni.navigateTo({
        url: `/myPackge5/pages/farm/penManage/addForm?pastureId=${this.pastureId}&pastureName=${this.pastureName}`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f7f8f7;
}

.content {
  height: calc(100vh - 126rpx);
  padding: 30rpx;
  box-sizing: border-box;
}

.farm-item {
  // height: 202rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  position: relative;
}

.farm-info {
  flex: 1;

  .farm-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .farm-name {
      font-weight: bold;
      font-size: 34rpx;
      color: #333333;
      margin-right: 18rpx;
    }

    .farm-nature {
      .nature-text {
        background: linear-gradient(140deg, #fff9f0 0%, #fff3d7 100%);
        color: #ff8c42;
        font-size: 26rpx;
        font-weight: 500;
        padding: 6rpx 16rpx;
        border-radius: 20rpx 0rpx 20rpx 0rpx;
        border: 1rpx solid #ffe4c4;
        line-height: 1.2;
        font-family: AlibabaPuHuiTi_3_105_Heavy;
      }
    }
  }
  .farm-title {
    display: flex;
    justify-content: space-between;
    line-height: 35px;
    color: #999999;
    text:nth-child(2) {
      color: #333333;
    }
  }

  .farm-address {
    font-weight: 400;
    font-size: 26rpx;
    color: #999999;
    line-height: 1.4;
  }
}

.edit-icon {
  position: absolute;
  right: 40rpx;
  width: 26rpx;
  height: 26rpx;
}

.status-text {
  text-align: center;
  padding: 40rpx 0;
  color: #999999;
  font-size: 28rpx;
}
.bg-box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 126rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.add-btn {
  width: 100%;
  height: 86rpx;
  background: linear-gradient(101deg, #19af77 0%, #40ca8f 100%);
  border-radius: 50rpx;
  font-weight: 600;
  font-size: 34rpx;
  color: #ffffff;
  text-align: center;
  line-height: 86rpx;
}
</style>
