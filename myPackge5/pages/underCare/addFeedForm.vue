<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
                    <u-form-item label="所在养殖场" required prop="pastureId" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getPastureText(form.pastureId)" placeholder="请选择养殖场" disabled
                            @click="!isDetail && (showPastureSelect = true)" />
                    </u-form-item>
                    <u-form-item label="投喂圈舍" required prop="penId" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getPenText(form.penId)" placeholder="请选择投喂圈舍" disabled
                            @click="!isDetail && form.pastureId && (showPenSelect = true)" />
                    </u-form-item>
                    <u-form-item label="饲料种类" required prop="feedFood" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getFeedFoodText(form.feedFood)" placeholder="请选择饲料种类" disabled
                            @click="!isDetail && (showFeedFoodSelect = true)" />
                    </u-form-item>
                    <u-form-item label="投喂数量（kg）" required prop="feedNum">
                        <u-input v-model="form.feedNum" placeholder="请输入投喂数量" type="number" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" :disabled="isDetail" maxlength="3" />
                    </u-form-item>
                    <u-form-item label="投喂频次" required prop="feedFrequency" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getFrequencyText(form.feedFrequency)" placeholder="请选择投喂频次" disabled
                            @click="!isDetail && (showFrequencySelect = true)" />
                    </u-form-item>
                    <u-form-item class="manageRemark" label="备注" prop="manageRemark" :border-bottom="false">
                        <u-input v-model="form.manageRemark" placeholder="请输入备注信息" type="textarea"
                            :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" maxlength="140" />
                    </u-form-item>
                </u-form>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <!-- 养殖场选择 -->
        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showPastureSelect" mode="single-column"
            :list="pastureList" label-name="label" @confirm="selectPasture" value-name="value" />

        <!-- 圈舍选择 -->
        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showPenSelect" mode="single-column"
            :list="penList" label-name="label" @confirm="selectPen" value-name="value" />

        <!-- 饲料种类选择 -->
        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showFeedFoodSelect" mode="single-column"
            :list="feedFoodList" label-name="label" @confirm="selectFeedFood" value-name="value" />

        <!-- 投喂频次选择 -->
        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showFrequencySelect" mode="single-column"
            :list="frequencyList" label-name="label" @confirm="selectFrequency" value-name="value" />
    </view>
</template>

<script>
import { penList, pasturePage } from '@/api/pages/livestock/farm'
import { feedAdd, feedDetail } from '@/api/pages/livestock/underCare'
import { getDicts } from "@/api/dict.js"
import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    name: 'addFeedForm',
    components: {
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            livestockManageId: '',
            isDetail: false,
            isSubmitting: false,
            form: {
                pastureId: '',
                penId: 0,
                feedFood: '',
                feedNum: '',
                feedFrequency: '',
                manageRemark: '',
            },
            // 样式配置
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            // 选择器状态
            showPastureSelect: false,
            showPenSelect: false,
            showFeedFoodSelect: false,
            showFrequencySelect: false,
            // 数据列表
            pastureList: [],
            penList: [],
            frequencyList: [],
            feedFoodList: [],
            rules: {
                pastureId: [{
                    required: true,
                    message: '请选择养殖场',
                    trigger: ['blur', 'change']
                }],
                penId: [{
                    required: true,
                    message: '请选择投喂圈舍',
                    trigger: ['blur', 'change']
                }],
                feedFood: [{
                    required: true,
                    message: '请选择饲料种类',
                    trigger: ['blur', 'change']
                }],
                feedNum: [{
                    required: true,
                    message: '请输入投喂数量',
                    trigger: ['blur', 'change']
                }],
                feedFrequency: [{
                    required: true,
                    message: '请选择投喂频次',
                    trigger: ['blur', 'change']
                }],
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        },
        pageTitle() {
            return this.isDetail ? '详情' : '新增饲养记录';
        },
        isFormValid() {
            const { pastureId, penId, feedFood, feedNum, feedFrequency } = this.form;
            return pastureId && penId !== '' && feedFood && feedNum && feedFrequency;
        },
    },

    onLoad(options) {
        this.livestockManageId = options.livestockManageId || '';
        this.isDetail = !!options.livestockManageId;

        this.getPastureList();
        this.getDictFrequency();
        this.getDictFeedfood();

        if (this.livestockManageId) {
            this.getDetail();
        }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        async getPastureList() {
            try {
                const params = { pageNum: 1, pageSize: 100 };
                const res = await pasturePage(params);
                if (res.code === 200) {
                    this.pastureList = res.result?.list?.map(item => ({
                        label: item.pastureName,
                        value: item.pastureId
                    })) || [];
                }
            } catch (error) {
                console.error('获取养殖场列表失败:', error);
            }
        },

        async getPenList() {
            if (!this.form.pastureId) {
                this.penList = [];
                return;
            }

            try {
                const params = { pastureId: this.form.pastureId };
                const res = await penList(params);
                if (res.code === 200) {
                    this.penList = res.result?.map(item => ({
                        label: item.penName,
                        value: item.penId
                    })) || [];
                }
            } catch (error) {
                console.error('获取圈舍列表失败:', error);
            }
        },

        async getDictFeedfood() {
            try {
                const res = await getDicts('pasture_feed_food');
                if (res && res.data) {
                    this.feedFoodList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载饲料种类字典失败:', error);
            }
        },

        async getDictFrequency() {
            try {
                const res = await getDicts('pasture_feed_frequency');
                if (res && res.data) {
                    this.frequencyList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载投喂频次字典失败:', error);
            }
        },

        async getDetail() {
            // TODO: 实现投喂详情获取逻辑
            console.log('获取投喂详情:', this.livestockManageId);
        },

        getPastureText(value) {
            const pasture = this.pastureList.find(item => item.value === value);
            return pasture ? pasture.label : '';
        },

        getPenText(value) {
            const pen = this.penList.find(item => item.value === value);
            return pen ? pen.label : '';
        },

        getFeedFoodText(value) {
            const feedFood = this.feedFoodList.find(item => item.value === value);
            return feedFood ? feedFood.label : '';
        },

        getFrequencyText(value) {
            const frequency = this.frequencyList.find(item => item.value === value);
            return frequency ? frequency.label : '';
        },

        selectPasture(value) {
            if (!value?.length) return;
            this.form.pastureId = value[0].value;
            this.showPastureSelect = false;
            this.resetField('pastureId');

            this.form.penId = 0;
            this.getPenList();
        },

        selectPen(value) {
            if (!value?.length) return;
            this.form.penId = value[0].value;
            this.showPenSelect = false;
            this.resetField('penId');
        },

        selectFeedFood(value) {
            if (!value?.length) return;
            this.form.feedFood = value[0].value;
            this.showFeedFoodSelect = false;
            this.resetField('feedFood');
        },

        selectFrequency(value) {
            if (!value?.length) return;
            this.form.feedFrequency = value[0].value;
            this.showFrequencySelect = false;
            this.resetField('feedFrequency');
        },

        async submitForm() {
            if (this.isSubmitting) return;

            if (!this.form.pastureId) {
                this.$toast('请先选择养殖场');
                return;
            }

            this.isSubmitting = true;
            try {
                const valid = await this.validateForm();
                if (!valid) return;

                const submitData = {
                    pastureId: this.form.pastureId,
                    penId: this.form.penId,
                    feedFood: this.form.feedFood,
                    feedFrequency: this.form.feedFrequency,
                    feedNum: this.form.feedNum,
                    manageRemark: this.form.manageRemark || ''
                };

                const res = await feedAdd(submitData);

                if (res.code === 200) {
                    uni.$emit('updateFeedList');
                    this.$toast('添加成功');
                    uni.navigateBack({ delta: 1 });
                } else {
                    throw new Error(res.message || '提交失败');
                }
            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        validateForm() {
            return new Promise(resolve => this.$refs.uForm.validate(resolve));
        },

        handleError(error, customMessage = '') {
            console.error(error);
            this.$toast(error.message || customMessage || '操作失败');
        },

        resetField(prop) {
            this.$refs.uForm?.fields?.find(field => field.prop === prop)?.resetField();
        }
    },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');


.manageRemark{
    /deep/ .u-form-item--left{
        align-items: start !important;
    }
    /deep/ .u-form-item--right{
        padding-top: 10rpx;
    }
}
</style>